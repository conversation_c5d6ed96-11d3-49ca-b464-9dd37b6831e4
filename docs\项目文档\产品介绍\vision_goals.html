<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品愿景与目标 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 愿景目标页面特定样式 */
      .vision-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .vision-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .vision-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
          linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%);
        background-size: 20px 20px;
        opacity: 0.3;
      }

      .vision-content {
        position: relative;
        z-index: 2;
      }

      .vision-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .vision-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .vision-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .vision-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .vision-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .vision-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .vision-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
        line-height: 1;
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
        vertical-align: middle;
        line-height: 1;
      }

      .vision-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
        margin: 0;
        line-height: 1.2;
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .vision-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .vision-card {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .vision-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .vision-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .vision-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container vision-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section vision-left">
          <div class="vision-content">
            <h1 class="vision-title">产品愿景与目标</h1>
            <p class="vision-subtitle">
              茂名市地质灾害预警平台以明确的愿景和目标为指引，致力于成为茂名市地质灾害防治的数字化基础设施，实现"人人知风险、处处有预警"。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="vision-grid">
            <!-- 产品愿景 -->
            <div class="vision-card">
              <div class="card-header">
                <i class="card-icon fas fa-eye"></i>
                <h3 class="card-title">产品愿景</h3>
              </div>
              <div class="card-body">
                成为<span class="highlight-text">茂名人民身边的地质安全守护者</span>，通过数字化手段让地质灾害风险信息触手可及，让安全防护深入人心，构建全民参与的地质灾害防治体系。
              </div>
            </div>

            <!-- 核心使命 -->
            <div class="vision-card">
              <div class="card-header">
                <i class="card-icon fas fa-heart"></i>
                <h3 class="card-title">核心使命</h3>
              </div>
              <div class="card-body">
                <span class="highlight-text">保护人民生命财产安全</span>，提升茂名市地质灾害防治科学化水平，为政府决策提供数据支撑，为公众提供便民服务，共同构建安全和谐的生活环境。
              </div>
            </div>

            <!-- 战略目标 -->
            <div class="vision-card">
              <div class="card-header">
                <i class="card-icon fas fa-bullseye"></i>
                <h3 class="card-title">战略目标</h3>
              </div>
              <div class="card-body">
                建设<span class="highlight-text">覆盖全市、服务全民</span>的地质灾害预警平台，提高预警信息发布覆盖率和公众知晓率，政府工作效率提升50%以上的目标。
              </div>
            </div>

            <!-- 发展方向 -->
            <div class="vision-card">
              <div class="card-header">
                <i class="card-icon fas fa-rocket"></i>
                <h3 class="card-title">发展方向</h3>
              </div>
              <div class="card-body">
                坚持<span class="highlight-text">创新驱动、服务导向</span>的发展理念，持续优化用户体验，拓展服务功能，逐步建成智慧化、精准化、便民化的现代地质灾害防治信息系统。
              </div>
            </div>
          </div>


        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="market_needs.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="core_features.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'market_needs.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'core_features.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
