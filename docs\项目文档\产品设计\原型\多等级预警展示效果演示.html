<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多等级预警展示效果演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 32px;
            font-size: 24px;
            font-weight: 600;
        }

        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
        }

        .demo-section h3 {
            margin-top: 0;
            color: #374151;
            font-size: 18px;
            margin-bottom: 16px;
        }

        /* 桌面端预警条演示 */
        .desktop-warning-bar {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            gap: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .warning-tag {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .warning-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .warning-tag.level-1 {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .warning-tag.level-2 {
            background: rgba(249, 115, 22, 0.1);
            color: #f97316;
            border: 1px solid rgba(249, 115, 22, 0.3);
        }

        .warning-icon {
            font-size: 16px;
        }

        /* 移动端预警横幅演示 */
        .mobile-warning-banner {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .mobile-warning-banner:active {
            transform: scale(0.98);
        }

        .warning-bell {
            font-size: 20px;
            animation: bell-swing 1s ease-in-out infinite;
        }

        @keyframes bell-swing {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(15deg); }
            75% { transform: rotate(-15deg); }
        }

        .warning-content {
            flex: 1;
        }

        .warning-main-text {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .warning-sub-text {
            font-size: 13px;
            opacity: 0.9;
        }

        .warning-hint {
            font-size: 12px;
            opacity: 0.8;
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 4px;
        }

        /* 详情面板演示 */
        .detail-panel {
            margin-top: 16px;
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e5e7eb;
        }

        .detail-header {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .warning-detail-item {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .warning-detail-item.level-1 {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.05);
        }

        .warning-detail-item.level-2 {
            border-left-color: #f97316;
            background: rgba(249, 115, 22, 0.05);
        }

        .detail-level {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .detail-level.level-1 { color: #ef4444; }
        .detail-level.level-2 { color: #f97316; }

        .detail-info {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        .town-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .town-tag {
            padding: 4px 8px;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .town-tag:hover {
            background: rgba(59, 130, 246, 0.2);
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 24px;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }

        .highlight-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }

        .highlight-title {
            color: #92400e;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .highlight-text {
            color: #78350f;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚨 多等级预警展示效果演示</h1>
        
        <div class="highlight-box">
            <div class="highlight-title">📋 演示说明</div>
            <div class="highlight-text">
                本演示展示了茂名市地质灾害预警平台在同时存在多个等级预警时的界面效果。
                当前模拟场景：同时生效一级预警（3个镇街）和二级预警（6个镇街）。
            </div>
        </div>

        <div class="comparison">
            <!-- 桌面端演示 -->
            <div class="demo-section">
                <h3>💻 桌面端效果</h3>
                <div class="desktop-warning-bar">
                    <div class="warning-tag level-1">
                        <span class="warning-icon">🔴</span>
                        <span>一级预警 3镇街</span>
                    </div>
                    <div class="warning-tag level-2">
                        <span class="warning-icon">🟠</span>
                        <span>二级预警 6镇街</span>
                    </div>
                    <div style="margin-left: auto; font-size: 14px; color: #6b7280;">
                        当前2级预警生效中
                    </div>
                </div>
                
                <div class="detail-panel">
                    <div class="detail-header">多等级预警详情</div>
                    
                    <div class="warning-detail-item level-1">
                        <div class="detail-level level-1">🔴 一级预警（红色）</div>
                        <div class="detail-info">
                            发布时间：2025-07-22 08:00<br>
                            影响区域：信宜市、高州市
                        </div>
                        <div class="town-tags">
                            <span class="town-tag">合水镇</span>
                            <span class="town-tag">钱排镇</span>
                            <span class="town-tag">新宝镇</span>
                        </div>
                    </div>
                    
                    <div class="warning-detail-item level-2">
                        <div class="detail-level level-2">🟠 二级预警（橙色）</div>
                        <div class="detail-info">
                            发布时间：2025-07-22 08:00<br>
                            影响区域：高州市、电白区
                        </div>
                        <div class="town-tags">
                            <span class="town-tag">古丁镇</span>
                            <span class="town-tag">深镇镇</span>
                            <span class="town-tag">荷花镇</span>
                            <span class="town-tag">罗坑镇</span>
                            <span class="town-tag">望夫镇</span>
                            <span class="town-tag">那霍镇</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 移动端演示 -->
            <div class="demo-section">
                <h3>📱 移动端效果</h3>
                <div class="mobile-warning-banner">
                    <div class="warning-bell">🔔</div>
                    <div class="warning-content">
                        <div class="warning-main-text">2级预警生效中</div>
                        <div class="warning-sub-text">一级、二级预警，涉及9个镇街</div>
                        <div class="warning-hint">
                            <span>点击查看详情</span>
                            <span>▼</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-panel">
                    <div class="detail-header">多等级预警详情</div>
                    
                    <div class="warning-detail-item level-1">
                        <div class="detail-level level-1">🔴 一级预警（红色）</div>
                        <div class="detail-info">
                            发布时间：2025-07-22 08:00<br>
                            涉及镇街：合水镇、钱排镇、新宝镇<br>
                            预警内容：预计未来24小时，降雨致地质灾害发生风险极高...
                        </div>
                    </div>
                    
                    <div class="warning-detail-item level-2">
                        <div class="detail-level level-2">🟠 二级预警（橙色）</div>
                        <div class="detail-info">
                            发布时间：2025-07-22 08:00<br>
                            涉及镇街：古丁镇、深镇镇、荷花镇等6个镇街<br>
                            预警内容：预计未来24小时，降雨致地质灾害发生风险高...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <div class="highlight-title">✅ 设计优势</div>
            <div class="highlight-text">
                • <strong>信息清晰</strong>：通过颜色和图标快速识别预警等级<br>
                • <strong>空间高效</strong>：紧凑布局不占用过多地图空间<br>
                • <strong>交互简单</strong>：保持现有用户习惯，一次点击获得详情<br>
                • <strong>响应式设计</strong>：桌面端和移动端都有最优体验
            </div>
        </div>
    </div>
</body>
</html>
